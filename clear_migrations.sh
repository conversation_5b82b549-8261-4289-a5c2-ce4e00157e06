#!/bin/bash

# 清除所有Django应用模块下的migrations文件，但保留__init__.py文件

echo "正在扫描Django应用..."
echo "=================================================="

# 查找所有包含migrations目录的Django应用（递归扫描，排除venv等目录）
apps_found=0
files_to_delete=()

# 使用find命令递归查找所有migrations目录，排除venv、.git等目录
while IFS= read -r -d '' migrations_dir; do
    # 获取相对路径
    rel_path="${migrations_dir#./}"
    app_name=$(dirname "$rel_path")

    # 跳过虚拟环境、git目录、缓存目录等
    if [[ "$rel_path" == venv/* ]] || [[ "$rel_path" == .git/* ]] || [[ "$rel_path" == __pycache__/* ]] || [[ "$rel_path" == .venv/* ]] || [[ "$rel_path" == env/* ]]; then
        continue
    fi

    echo "找到Django应用: $app_name"
    ((apps_found++))

    # 进入migrations目录扫描文件
    cd "$migrations_dir" || continue

    # 查找所有要删除的.py文件（除了__init__.py）
    for file in *.py; do
        if [ "$file" != "__init__.py" ] && [ "$file" != "*.py" ]; then
            files_to_delete+=("$rel_path/$file")
        fi
    done

    # 查找__pycache__目录
    if [ -d "__pycache__" ]; then
        files_to_delete+=("$rel_path/__pycache__/")
    fi

    # 返回根目录
    cd - > /dev/null
done < <(find . -type d -name "migrations" -not -path "./venv/*" -not -path "./.git/*" -not -path "./.venv/*" -not -path "./env/*" -print0)

if [ $apps_found -eq 0 ]; then
    echo "未找到任何包含migrations目录的Django应用"
    exit 0
fi

echo ""
echo "总共找到 $apps_found 个Django应用"
echo "=================================================="

# 显示所有要删除的文件
if [ ${#files_to_delete[@]} -eq 0 ]; then
    echo "没有找到需要清除的migrations文件"
    exit 0
fi

echo "以下文件和目录将被删除 (保留__init__.py):"
echo ""
for item in "${files_to_delete[@]}"; do
    if [[ "$item" == *"__pycache__/" ]]; then
        echo "  [目录] $item"
    else
        echo "  [文件] $item"
    fi
done

echo ""
echo "总计: ${#files_to_delete[@]} 个文件/目录"
echo "=================================================="

# 询问用户确认
read -p "确定要删除以上文件吗？[y/N]: " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始清除migrations文件..."
echo "=================================================="

total_cleared=0
total_errors=0

# 删除文件
for item in "${files_to_delete[@]}"; do
    if [[ "$item" == *"__pycache__/" ]]; then
        # 删除目录
        if rm -rf "$item" 2>/dev/null; then
            echo "  ✓ 删除目录: $item"
            ((total_cleared++))
        else
            echo "  ✗ 删除目录失败: $item"
            ((total_errors++))
        fi
    else
        # 删除文件
        if rm "$item" 2>/dev/null; then
            echo "  ✓ 删除文件: $item"
            ((total_cleared++))
        else
            echo "  ✗ 删除文件失败: $item"
            ((total_errors++))
        fi
    fi
done

echo ""
echo "=================================================="
echo "清除完成!"
echo "总计清除: $total_cleared 个文件/目录"
if [ $total_errors -gt 0 ]; then
    echo "错误数量: $total_errors"
fi
echo ""
echo "注意: 所有__init__.py文件已保留"
