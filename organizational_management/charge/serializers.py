from rest_framework import serializers

from core.model import BaseModel
from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.room.serializers import RoomSerializer
from organizational_management.charge.models import MaternityCostInfo, MaternityRenewCostInfo, Package
from organizational_management.charge.enum import PaymentMethodEnum, PaymentStatusEnum

# 套餐序列化器
class PackageSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    
    
    class Meta:
        model = Package
        exclude = ["maternity_center",'id']


# 更新套餐
class PackageUpdateSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = Package
        fields = ["id","name", "description", "price", "stay_days", "status", "created_at", "updated_at"]
        
    def validate_price(self, value):
        if value <= 0:
            raise serializers.ValidationError("套餐价格不能小于等于0")
        return value
    
    def validate_stay_days(self, value):
        if value <= 0 or value > 365:
            raise serializers.ValidationError("套餐天数不能小于等于0或大于365")
        return value



        
# 产妇入院费用信息管理
class MaternityCostInfoSerializer(serializers.ModelSerializer):
    
    package = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = MaternityCostInfo
        exclude = ['maternity_center','id']
        
    def get_package(self, obj):
        if obj.package:
            return PackageSerializer(obj.package).data
        return None
    

# 产妇入院费用信息管理创建序列化器
class MaternityCostInfoCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityCostInfo
        fields = ["maternity_admission","package","package_price","deposit_amount","earnest_amount","payable_amount","paid_amount","remaining_amount","payment_method","remark","payment_status"]
        

# 续住费用信息序列化器
class MaternityRenewCostInfoSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityRenewCostInfo
        fields = ['renew_days','renew_cost','renew_cost_remark','rbid']



# 结算单列表序列化器
class BillListSerializer(serializers.ModelSerializer):
    # 产妇姓名
    maternity_name = serializers.SerializerMethodField(read_only=True)
    # 房间号
    room_number = serializers.SerializerMethodField(read_only=True)
    # 套餐类型
    package_type = serializers.SerializerMethodField(read_only=True)
    # 入住日期
    check_in_date = serializers.SerializerMethodField(read_only=True)
    # 退房日期
    check_out_date = serializers.SerializerMethodField(read_only=True)
    # 支付状态显示
    payment_status_display = serializers.SerializerMethodField(read_only=True)
    # 是否有续住
    has_renew = serializers.SerializerMethodField(read_only=True)
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    
    class Meta:
        model = MaternityCostInfo
        fields = ['bid','maternity_name','room_number','package_type','check_in_date','check_out_date','payable_amount','payment_status','payment_status_display','has_renew','created_at','remark']
        
    # 获取产妇姓名
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission and obj.maternity_admission.maternity else "N/A"
    
    # 获取房间号
    def get_room_number(self, obj):
        return obj.maternity_admission.room.room_number if obj.maternity_admission and obj.maternity_admission.room else "N/A"
    
    # 获取套餐类型
    def get_package_type(self, obj):
        return obj.package.name if obj.package else "N/A"

    def get_check_in_date(self, obj):
        
        admission = obj.maternity_admission
        
        if admission.actual_check_in_date:
            return admission.actual_check_in_date
        
        elif admission.expected_check_in_date:
            return f'{admission.expected_check_in_date}(预计)'
        
        else:
            return "N/A"

    def get_check_out_date(self, obj):
        
        admission = obj.maternity_admission
        
        if admission.actual_check_out_date:
            return admission.actual_check_out_date
        
        elif admission.expected_check_out_date:
            return f'{admission.expected_check_out_date}(预计)'
        
        else:
            return "N/A"
    
    # 获取支付状态显示
    def get_payment_status_display(self, obj):
        return obj.get_payment_status_display()

    def get_has_renew(self, obj):
        return obj.maternity_renew_cost_info.count() > 0


# 结算单（入院记录）序列化器
class BillMaternityAdmissionSerializer(serializers.ModelSerializer):
    # 产妇姓名
    maternity_name = serializers.SerializerMethodField(read_only=True)
    # 房间号
    room = serializers.SerializerMethodField(read_only=True)
    # 入住日期
    check_in_date = serializers.SerializerMethodField(read_only=True)
    # 退房日期
    check_out_date = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = MaternityAdmission
        fields = ["maternity_name","room","check_in_date","check_out_date"]

    # 获取产妇姓名
    def get_maternity_name(self, obj):
        return obj.maternity.name if obj.maternity else "N/A"
    
    # 获取房间号
    def get_room_number(self, obj):
        return obj.room.room_number if obj.room else "N/A"
    
    def get_room(self, obj):
        data = RoomSerializer(obj.room).data
        
        return {
            'room_number':data['room_number'],
            'room_type':data['room_type'],
        }
    
    def get_check_in_date(self, obj):
    
        
        if obj.actual_check_in_date:
            return obj.actual_check_in_date
        
        elif obj.expected_check_in_date:
            return f'{obj.expected_check_in_date}(预计)'
        
        else:
            return "N/A"

    def get_check_out_date(self, obj):
        
        
        if obj.actual_check_out_date:
            return obj.actual_check_out_date
        
        elif obj.expected_check_out_date:
            return f'{obj.expected_check_out_date}(预计)'
        
        else:
            return "N/A"
    
    

# 结算单详情序列化器
class BillDetailSerializer(serializers.ModelSerializer):
    # 套餐
    package = PackageSerializer(read_only=True)
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField(read_only=True)
    # 入院记录
    maternity_admission = BillMaternityAdmissionSerializer(read_only=True)
    # 续住入院记录
    ma_renew = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = MaternityCostInfo
        exclude = ["maternity_center","id"]
        
    def get_ma_renew(self, obj):
        if obj.maternity_renew_cost_info:
            return MaternityRenewCostInfoSerializer(obj.maternity_renew_cost_info.all(),many=True).data
        return None

        


# 结算单更新序列化器
class BillUpdateSerializer(serializers.ModelSerializer):
    
    package = serializers.CharField(required=False)
    
    class Meta:
        model = MaternityCostInfo
        fields = ["package","package_price","deposit_amount","earnest_amount","payable_amount","paid_amount","remaining_amount","payment_method","remark","payment_status"]
    
    def validate_package(self, value):
        
        package = Package.get_package_by_rid(value,self.context['request'].user.maternity_center)
        
        if not package:
            raise serializers.ValidationError("套餐不存在")
        
        return package

# 续住费用信息列表序列化器
class RenewCostInfoListSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityRenewCostInfo
        fields = ['rbid','renew_days','renew_cost','renew_cost_remark']

# 续住费用信息更新序列化器
class RenewCostInfoUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityRenewCostInfo
        fields = ['renew_days','renew_cost','renew_cost_remark']
        
    