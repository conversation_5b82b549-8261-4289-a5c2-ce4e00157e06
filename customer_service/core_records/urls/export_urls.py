from django.urls import path

from customer_service.core_records.views import export_views

urlpatterns = [
    path('export/maternity-rehabilitation/', export_views.MaternityRehabilitationAssessmentRecordExportView.as_view(), name='maternity_rehabilitation_export'),
    path('export/maternity-physical-care/', export_views.MaternityDailyPhysicalCareRecordExportView.as_view(), name='maternity_physical_care_export'),
    path('export/maternity-diet/', export_views.MaternityDailyDietRecordExportView.as_view(), name='maternity_diet_export'),
    
    path('export/newborn-care-one/', export_views.NewbornCareOneRecordExportView.as_view(), name='newborn_care_one_export'),
    path('export/newborn-care-two/', export_views.NewbornCareTwoRecordExportView.as_view(), name='newborn_care_two_export'),
    path('export/newborn-feeding/', export_views.NewbornFeedingRecordExportView.as_view(), name='newborn_feeding_export'),
    path('export/newborn-care-operation/', export_views.NewbornCareOperationRecordExportView.as_view(), name='newborn_care_operation_export'),
    
    path('export/maternity-checkin/', export_views.MaternityCheckInAssessmentExportView.as_view(), name='maternity_checkin_export'),
    path('export/newborn-checkin/', export_views.NewbornCheckInAssessmentExportView.as_view(), name='newborn_checkin_export'),
    path('export/newborn-month/', export_views.NewbornMonthAssessmentExportView.as_view(), name='newborn_month_export'),
]