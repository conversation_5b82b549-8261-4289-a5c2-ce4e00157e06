"""
数据迁移脚本：将CharField字段的枚举值迁移到JSONField格式

使用方法：
python manage.py migrate_char_to_json

说明：
这个脚本将以下字段从CharField格式迁移到JSONField（列表）格式：
1. MaternityRehabilitationAssessmentRecord.incision_situation
2. NewbornMonthAssessment.skin

迁移逻辑：
- 如果原字段为空字符串或None，则设置为空列表 []
- 如果原字段包含有效的枚举值，则将其转换为包含该值的列表 [value]
- 如果原字段包含无效值，则设置为空列表 [] 并记录日志
"""

import json
from django.core.management.base import BaseCommand
from django.db import transaction
from customer_service.core_records.models.maternal import MaternityRehabilitationAssessmentRecord
from customer_service.core_records.models.baby import NewbornMonthAssessment
from customer_service.core_records.enums.maternal import RehabilitationIncisionSituationEnum
from customer_service.core_records.enums.baby import MonthSkinStatusChoice


class Command(BaseCommand):
    help = '将CharField格式的枚举字段迁移到JSONField格式'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='执行干跑模式，不实际修改数据库',
        )
        parser.add_argument(
            '--verbose',
            action='store_true', 
            help='输出详细信息',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('执行干跑模式，不会实际修改数据库'))
        
        self.stdout.write('开始数据迁移...')
        
        # 迁移产妇康复护理评估记录的切口情况字段
        self._migrate_incision_situation(dry_run, verbose)
        
        # 迁移新生儿满月评估的皮肤字段  
        self._migrate_skin_field(dry_run, verbose)
        
        self.stdout.write(self.style.SUCCESS('数据迁移完成！'))

    def _migrate_incision_situation(self, dry_run, verbose):
        """迁移MaternityRehabilitationAssessmentRecord.incision_situation字段"""
        
        self.stdout.write('\n正在迁移 MaternityRehabilitationAssessmentRecord.incision_situation 字段...')
        
        # 获取所有有效的枚举值
        valid_choices = [choice[0] for choice in RehabilitationIncisionSituationEnum.choices]
        
        # 查询所有记录，假设原来是CharField存储的
        # 注意：由于字段已经改为JSONField，我们需要检查数据类型
        records = MaternityRehabilitationAssessmentRecord.objects.all()
        
        updated_count = 0
        error_count = 0
        
        with transaction.atomic():
            for record in records:
                try:
                    old_value = record.incision_situation
                    new_value = None
                    
                    # 检查当前值的类型和内容
                    if old_value is None or old_value == '':
                        # 空值转换为空列表
                        new_value = []
                    elif isinstance(old_value, str):
                        # 字符串值：检查是否为有效枚举值
                        if old_value in valid_choices:
                            new_value = [old_value]
                        else:
                            # 无效值设置为空列表
                            new_value = []
                            if verbose:
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'记录 ID {record.id}: 无效的切口情况值 "{old_value}"，已设置为空列表'
                                    )
                                )
                    elif isinstance(old_value, list):
                        # 已经是列表格式，验证内容
                        new_value = [v for v in old_value if v in valid_choices]
                        if len(new_value) != len(old_value) and verbose:
                            self.stdout.write(
                                self.style.WARNING(
                                    f'记录 ID {record.id}: 过滤了无效的切口情况值，保留 {new_value}'
                                )
                            )
                    else:
                        # 其他类型转换为空列表
                        new_value = []
                        if verbose:
                            self.stdout.write(
                                self.style.WARNING(
                                    f'记录 ID {record.id}: 未知类型的切口情况值，已设置为空列表'
                                )
                            )
                    
                    # 更新记录
                    if new_value != old_value:
                        if not dry_run:
                            record.incision_situation = new_value
                            record.save(update_fields=['incision_situation'])
                        updated_count += 1
                        
                        if verbose:
                            self.stdout.write(f'记录 ID {record.id}: {old_value} -> {new_value}')
                    
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'处理记录 ID {record.id} 时出错: {str(e)}')
                    )
        
        self.stdout.write(f'切口情况字段迁移完成：更新了 {updated_count} 条记录，{error_count} 条记录出错')

    def _migrate_skin_field(self, dry_run, verbose):
        """迁移NewbornMonthAssessment.skin字段"""
        
        self.stdout.write('\n正在迁移 NewbornMonthAssessment.skin 字段...')
        
        # 获取所有有效的枚举值
        valid_choices = [choice[0] for choice in MonthSkinStatusChoice.choices]
        
        # 查询所有记录
        records = NewbornMonthAssessment.objects.all()
        
        updated_count = 0
        error_count = 0
        
        with transaction.atomic():
            for record in records:
                try:
                    old_value = record.skin
                    new_value = None
                    
                    # 检查当前值的类型和内容
                    if old_value is None or old_value == '':
                        # 空值转换为空列表
                        new_value = []
                    elif isinstance(old_value, str):
                        # 字符串值：检查是否为有效枚举值
                        if old_value in valid_choices:
                            new_value = [old_value]
                        else:
                            # 无效值设置为空列表
                            new_value = []
                            if verbose:
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'记录 ID {record.id}: 无效的皮肤状态值 "{old_value}"，已设置为空列表'
                                    )
                                )
                    elif isinstance(old_value, list):
                        # 已经是列表格式，验证内容
                        new_value = [v for v in old_value if v in valid_choices]
                        if len(new_value) != len(old_value) and verbose:
                            self.stdout.write(
                                self.style.WARNING(
                                    f'记录 ID {record.id}: 过滤了无效的皮肤状态值，保留 {new_value}'
                                )
                            )
                    else:
                        # 其他类型转换为空列表
                        new_value = []
                        if verbose:
                            self.stdout.write(
                                self.style.WARNING(
                                    f'记录 ID {record.id}: 未知类型的皮肤状态值，已设置为空列表'
                                )
                            )
                    
                    # 更新记录
                    if new_value != old_value:
                        if not dry_run:
                            record.skin = new_value
                            record.save(update_fields=['skin'])
                        updated_count += 1
                        
                        if verbose:
                            self.stdout.write(f'记录 ID {record.id}: {old_value} -> {new_value}')
                    
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'处理记录 ID {record.id} 时出错: {str(e)}')
                    )
        
        self.stdout.write(f'皮肤字段迁移完成：更新了 {updated_count} 条记录，{error_count} 条记录出错') 