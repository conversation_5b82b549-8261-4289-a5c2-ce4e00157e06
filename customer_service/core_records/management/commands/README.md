# 数据迁移脚本使用说明

## migrate_char_to_json.py

### 功能说明
这个脚本用于将以下两个字段从CharField格式迁移到JSONField格式：

1. **MaternityRehabilitationAssessmentRecord.incision_situation** (切口情况)
   - 枚举值：UNKNOWN, NORMAL, RED, SECRETION
   
2. **NewbornMonthAssessment.skin** (皮肤状态)  
   - 枚举值：UNKNOWN, INTACT, DAMAGED, RED, YELLOW, RED_BUTTOCKS, RASH

### 迁移逻辑
- 空值或空字符串 → 空列表 `[]`
- 有效的枚举值字符串 → 包含该值的列表 `[value]`
- 无效值 → 空列表 `[]` (会记录警告日志)
- 已经是列表格式 → 过滤保留有效值

### 使用方法

#### 1. 干跑模式（推荐先执行）
```bash
python manage.py migrate_char_to_json --dry-run --verbose
```
这会模拟执行迁移过程，显示详细信息但不实际修改数据库。

#### 2. 正式执行迁移
```bash
python manage.py migrate_char_to_json --verbose
```
执行实际的数据迁移。

#### 3. 静默执行
```bash
python manage.py migrate_char_to_json
```
只显示基本的迁移结果信息。

### 参数说明
- `--dry-run`: 干跑模式，不实际修改数据库
- `--verbose`: 显示详细的迁移过程信息

### 注意事项
1. **执行前备份**：建议在执行迁移前备份数据库
2. **先执行干跑**：推荐先使用 `--dry-run` 参数查看迁移效果
3. **事务安全**：脚本使用数据库事务，确保数据一致性
4. **幂等性**：脚本可以重复执行，已经是正确格式的数据不会被修改

### 示例输出
```
开始数据迁移...

正在迁移 MaternityRehabilitationAssessmentRecord.incision_situation 字段...
记录 ID 123: "NORMAL" -> ["NORMAL"]
记录 ID 124: "" -> []
切口情况字段迁移完成：更新了 25 条记录，0 条记录出错

正在迁移 NewbornMonthAssessment.skin 字段...
记录 ID 456: "INTACT" -> ["INTACT"]  
记录 ID 457: "DAMAGED" -> ["DAMAGED"]
皮肤字段迁移完成：更新了 18 条记录，0 条记录出错

数据迁移完成！
``` 