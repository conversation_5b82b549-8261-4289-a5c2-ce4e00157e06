# 导出word文档
<模板文件名>  <模板行数>行<每行内容纳的字符行数>
<逗号分隔的每列宽度（字符数）>

当每行中的每列字符数限制在 (列宽*每行内容纳的字符行数) 及以内时，导出文件行数为 模板行数

maternity_rehabilitation 16行2
5, 5, 5, 5, 5,   3, 6, 2, 2, 2,   2, 2, 3,   7, 3

maternity_physical_care 18行3
5, 3, 3,   9,   3, 3,   3, 3, 3, 4,   3, 9, 5,   3

maternity_diet   10行3
5, 3, 3,   4, 3,   4, 3, 3,   3, 3, 3,   3, 4, 5,   5, 3, 6

newborn_care_one   17行2
5, 5, 8,    5, 3, 5, 3,    5, 3, 3,    3, 9, 3

newborn_care_two    11行3
5, 2, 2, 2, 2, 2, 2, 2,    2, 3,   2, 2, 2,   2, 2, 2,   2, 2, 3,  3, 2

newborn_feeding    23行2
9, 6, 6,   6, 6,   5, 5,   9, 3

newborn_care_operation   18行3
5, 5, 5, 4,   4, 4, 4, 4, 4,   14, 9, 3