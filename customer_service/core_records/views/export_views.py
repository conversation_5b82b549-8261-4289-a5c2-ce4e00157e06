from io import BytesIO
import math
from django.db import models
from django.utils import timezone
from docxtpl import DocxTemplate, R
from datetime import datetime, date, time
from django.http import HttpResponse
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import BloodTypeEnum, DeliveryMethodEnum, GenderEnum
from core.parse_time import parse_datetime_to_shanghai_time
from core.resp import make_response
from customer_service.core_records.enums.baby import BowelMovementChoice, FeedingMethodChoice, UrinationChoice
from customer_service.core_records.enums.maternal import DefecationEnum, EatingSituationEnum, ExerciseAfterEvaluationEnum, RehabilitationIncisionSituationEnum, SpecialDietFeaturesEnum, UrinationEnum
from customer_service.core_records.models.baby import Newborn, NewbornCareOneRecord, NewbornCareOperationRecord, NewbornCareTwoRecord, NewbornCheckInAssessment, NewbornFeedingRecord, NewbornMonthAssessment
from customer_service.core_records.models.maternal import MaternityCheckInAssessment, MaternityDailyDietRecord, MaternityDailyPhysicalCareRecord, MaternityRehabilitationAssessmentRecord
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from permissions.enum import PermissionEnum


class ExportBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.REPORT_AND_EXPORT_CENTER_EDIT
    
    export_model = None                 # 导出模型
    export_fields = []                  # 导出字段
    export_context_fields = []          # 参考字段，用于customize_row_data
    export_template_file_name = ""      # 导出模板文件名
    export_file_name = "导出文件"       # 导出文件名
    order_by = "record_date"            # 排序字段
    is_table = True                     # 是否表格，表格则根据列宽，页高，自动分页
    fixed_row_count = 0                 # 表格模式下，固定行数

    col_width = []                      # 列宽(字符数)，例如：[1, 2, 3]，表示第一列宽为1，第二列宽为2，第三列宽为3
    page_height = 45                    # 页高
    
    enum_to_label = False
    use_local_time = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.empty_row_height = self.page_height / self.fixed_row_count
        self.docx_context = {'PAGE_BREAK': '\f'}
        self.maternity_center = None

    # 可重写添加select_related
    def get_base_queryset(self):
        base_queryset = self.export_model.objects
        if self.order_by:
            base_queryset = base_queryset.order_by(self.order_by)
        return base_queryset

    # 可重写自定义数据
    def customize_row_data(self, row):
        """
        自定义行数据处理，将enum字段的值转换为对应的label
        """
        if 'record_date' in row and isinstance(row['record_date'], date):
            row['record_date'] = row['record_date'].strftime('%m-%d\n%Y')
        for k, v in row.items():
            if v is None or v == '':
                if self.is_table:
                    row[k] = ''
                else: # 插入空格延伸word下划线
                    row[k] = '  '
        # 将枚举值转换为label
        if self.enum_to_label or self.use_local_time:
            try:
                fields = self.export_model._meta.get_fields()
            except AttributeError:
                return row
            for field in fields:
                # 检查字段是否在导出字段中，且有choices属性（即enum字段）
                if not hasattr(field, 'name') or field.name not in row:
                    continue
                if self.enum_to_label and hasattr(field, 'choices') and field.choices:
                    field_value = row[field.name]
                    if field_value:
                        # 创建choices的字典映射 {value: label}
                        choices_dict = dict(field.choices)
                        # 如果当前值在choices中，替换为对应的label
                        if field_value in choices_dict:
                            row[field.name] = choices_dict[field_value]
                if self.use_local_time and row[field.name] and isinstance(field, models.DateTimeField):
                    row[field.name] = timezone.localtime(row[field.name]).strftime('%Y-%m-%d %H:%M:%S')
        return row

    def get(self, request):
        self.maternity_center = request.user.maternity_center
        if not self.export_fields:
            return make_response(code=-1, msg="导出字段不能为空")
        if not self.export_template_file_name:
            return make_response(code=-1, msg="导出模板文件不能为空")
        data = self.get_base_queryset().values(*self.export_fields, *self.export_context_fields)
        if not data:
            return make_response(code=-1, msg="数据为空")
        if self.is_table:
            # 生成context
            pages = []
            page = []
            current_page_height = 0
            for row in data:
                row = self.customize_row_data(row)
                row_context = {}
                row_height = 0
                # debug_list = []
                for col_idx, field in enumerate(self.export_fields):
                    text = row[field]
                    text_height = len(str(text)) / self.col_width[col_idx]
                    # debug_list.append((len(str(text)), self.col_width[col_idx], text_height))
                    if text_height > row_height:
                        row_height = text_height
                    row_context['col'+str(col_idx)] = text
                row_height = math.ceil(row_height)
                # print(debug_list)
                # print(row_height)
                # 换页判断
                if current_page_height + row_height > self.page_height\
                    or (self.fixed_row_count > 0 and len(page) >= self.fixed_row_count):
                    # print("-------")
                    # print(f"{current_page_height},{row_height},{self.page_height}|{len(page)}, {self.fixed_row_count}")
                    pages.append(page)
                    page = []
                    current_page_height = 0
                page.append(row_context)
                current_page_height += row_height
            if page:
                if self.fixed_row_count > 0 and len(page) < self.fixed_row_count:
                    empty_row_count = min(self.fixed_row_count - len(page), (self.page_height-current_page_height) // self.empty_row_height)
                    for i in range(int(empty_row_count)):
                        page.append(dict())
                        current_page_height += self.empty_row_height
                pages.append(page)
        # 渲染模板
        tpl = DocxTemplate(self.export_template_file_name)
        if self.is_table:
            render_context = {"pages": pages}
        else:
            render_context = self.customize_row_data(data[0])
        if self.docx_context:
            render_context.update(self.docx_context)
        tpl.render(render_context)
        # 保存文件
        buffer = BytesIO()
        tpl.save(buffer)
        buffer.seek(0)

        response = HttpResponse(
            buffer,
            content_type=(
                "application/"
                "vnd.openxmlformats-officedocument.wordprocessingml.document"
            ),
        )
        response["Content-Disposition"] = (
            f'attachment; filename="{self.export_file_name}.docx"'
        )
        return response
    

class ExportByRecordDateBaseView(ExportBaseView):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.start_date_obj = None
        self.end_date_obj = None
    
    def get_base_queryset(self):
        base_queryset = super().get_base_queryset()
        if self.start_date_obj:
            if hasattr(self.export_model, 'record_date'):
                base_queryset = base_queryset.filter(record_date__gte=self.start_date_obj)
            elif hasattr(self.export_model, 'record_time'):
                start_datetime = timezone.make_aware(datetime.combine(self.start_date_obj, time.min))
                base_queryset = base_queryset.filter(record_time__gte=start_datetime)
        if self.end_date_obj:
            if hasattr(self.export_model, 'record_date'):
                base_queryset = base_queryset.filter(record_date__lte=self.end_date_obj)
            elif hasattr(self.export_model, 'record_time'):
                end_datetime = timezone.make_aware(datetime.combine(self.end_date_obj, time.max))
                base_queryset = base_queryset.filter(record_time__lte=end_datetime)
        return base_queryset
    
    def get(self, request):
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        # 校验并转换日期
        if start_date_str:
            try:
                self.start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError:
                return make_response(code=-1, msg="开始日期格式错误，请使用YYYY-MM-DD格式")
        else:
            self.start_date_obj = None
        if end_date_str:
            try:
                self.end_date_obj = datetime.strptime(end_date_str, '%Y-%m-%d')
            except ValueError:
                return make_response(code=-1, msg="结束日期格式错误，请使用YYYY-MM-DD格式")
        else:
            self.end_date_obj = None
        # 校验日期逻辑
        if self.start_date_obj and self.end_date_obj and self.start_date_obj > self.end_date_obj:
            return make_response(code=-1, msg="开始日期不能大于结束日期")
        return super().get(request)


class MaternityTableExportBaseView(ExportByRecordDateBaseView):
    table_file_name = ""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.aid = None
    
    def get_base_queryset(self):
        return super().get_base_queryset().filter(
            maternity_admission__maternity_center=self.maternity_center,
            maternity_admission__aid=self.aid
        )
    
    def get(self, request):
        self.aid = request.query_params.get('aid')
        if not self.aid:
            return make_response(code=-1, msg="入院信息编号不能为空")
        try:
            maternity_admission = MaternityAdmission.objects.select_related(
                'maternity', 'room'
            ).get(
                aid=self.aid, maternity_center=request.user.maternity_center
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院信息不存在")
        if maternity_admission.room:
            self.docx_context['r_num'] = maternity_admission.room.room_number
        else:
            self.docx_context['r_num'] = "未知"
        if maternity_admission.maternity.name:
            self.docx_context['name'] = maternity_admission.maternity.name
            self.export_file_name = str(maternity_admission.maternity.name) + '-' + self.table_file_name
        else:
            self.docx_context['name'] = "未知"
            self.export_file_name = '产妇' + self.table_file_name
        self.docx_context['a'] = '☐'
        self.docx_context['b'] = '☐'
        self.docx_context['c'] = '☐'
        if maternity_admission.delivery_method == DeliveryMethodEnum.VAGINAL:
            self.docx_context['a'] = '☑'
        elif maternity_admission.delivery_method == DeliveryMethodEnum.CESAREAN:
            self.docx_context['b'] = '☑'
        elif maternity_admission.delivery_method == DeliveryMethodEnum.FORCEPS:
            self.docx_context['c'] = '☑'
        return super().get(request)
    

class MaternityRehabilitationAssessmentRecordExportView(MaternityTableExportBaseView):
    export_model = MaternityRehabilitationAssessmentRecord
    export_fields = [
        'record_date', 'postpartum_days', 'temperature', 'pulse', 'weight',
        'uterus_height', 'incision_situation', 'lochia_amount', 'lochia_color', 'lochia_smell',
        'milk_amount', 'breast_pain', 'nipple_cracked',
        'guidance_opinion', 'signature'
    ]
    export_template_file_name = "customer_service/core_records/templates/export_templates/maternity_rehabilitation.docx"
    col_width = [5, 5, 5, 5, 5, 3, 6, 2, 2, 2, 2, 2, 3, 7, 3]
    page_height = 32
    fixed_row_count = 16
    table_file_name = "康复护理评估记录"
    
    def customize_row_data(self, row):
        incision_situations = ""
        for incision_situation in row['incision_situation']:
            if incision_situation == RehabilitationIncisionSituationEnum.NORMAL:
                incision_situations += 'A'
            elif incision_situation == RehabilitationIncisionSituationEnum.RED:
                incision_situations += 'B'
            elif incision_situation == RehabilitationIncisionSituationEnum.SECRETION:
                incision_situations += 'C'
        row['incision_situation'] = incision_situations
        return super().customize_row_data(row)

    
class MaternityDailyPhysicalCareRecordExportView(MaternityTableExportBaseView):
    export_model = MaternityDailyPhysicalCareRecord
    export_fields = [
        'record_date', 'normal_diet', 'special_diet', 'sleep_hours', 'urination', 'defecation',
        'brushing_teeth', 'wash_hair', 'bathe', 'perineal_cleaning',
        'exercise_time', 'exercise_method',
        'other_situation', 'signature'
    ]
    export_context_fields = ['exercise_tolerance', 'exercise_understanding', 'heart_rate', 'blood_pressure']
    export_template_file_name = "customer_service/core_records/templates/export_templates/maternity_physical_care.docx"
    col_width = [5, 3, 3, 9, 3, 3, 3, 3, 3, 4, 3, 7, 5, 3]
    page_height = 54
    fixed_row_count = 18
    table_file_name = "每日生理护理记录"
        
    def customize_row_data(self, row):
        if 'exercise_method' not in row or not row['exercise_method']:
            row['exercise_time'] = ""
        if 'exercise_time' not in row or not row['exercise_time']:
            row['exercise_tolerance'] = ""
            row['exercise_understanding'] = ""
        
        if row['exercise_tolerance'] == ExerciseAfterEvaluationEnum.GOOD:
            row['exercise_method'] = row['exercise_method'] + '1A'
        elif row['exercise_tolerance'] == ExerciseAfterEvaluationEnum.AVERAGE:
            row['exercise_method'] = row['exercise_method'] + '1B'
        elif row['exercise_tolerance'] == ExerciseAfterEvaluationEnum.POOR:
            row['exercise_method'] = row['exercise_method'] + '1C'
        elif row['exercise_understanding']:
            row['exercise_method'] = row['exercise_method'] + ''
        
        if row['exercise_understanding'] == ExerciseAfterEvaluationEnum.GOOD:
            row['exercise_method'] = row['exercise_method'] + '2A'
        elif row['exercise_understanding'] == ExerciseAfterEvaluationEnum.AVERAGE:
            row['exercise_method'] = row['exercise_method'] + '2B'
        elif row['exercise_understanding'] == ExerciseAfterEvaluationEnum.POOR:
            row['exercise_method'] = row['exercise_method'] + '2C'
        
        if row['heart_rate']:
            row['exercise_method'] = row['exercise_method'] + '\n心跳:' + str(row['heart_rate'])
        if row['blood_pressure']:
            row['exercise_method'] = row['exercise_method'] + '\n血压:' + str(row['blood_pressure'])
        return super().customize_row_data(row)
    

class MaternityDailyDietRecordExportView(MaternityTableExportBaseView):
    export_model = MaternityDailyDietRecord
    export_fields = [
        'record_date', 'meal_number', 'eating_situation', 'normal_diet_features', 'special_diet_features',
        'carbohydrate', 'vegetables_class', 'fruit_class', 'meat_class', 'poultry_class', 'fish_class',
        'egg_class', 'soy_products_class', 'milk', 'dairy_products_class', 'oil_class', 'chinese_medicines_class'
    ]
    export_template_file_name = "customer_service/core_records/templates/export_templates/maternity_diet.docx"
    col_width = [5, 3, 3, 4, 3, 4, 3, 3, 3, 3, 3, 3, 4, 5, 5, 3, 6]
    page_height = 30
    fixed_row_count = 10
    table_file_name = "膳食记录"
    
    def customize_row_data(self, row):
        if row['eating_situation'] == EatingSituationEnum.FINISHED:
            row['eating_situation'] = 'A'
        elif row['eating_situation'] == EatingSituationEnum.REMAINING_LITTLE:
            row['eating_situation'] = 'B'
        elif row['eating_situation'] == EatingSituationEnum.REMAINING_MUCH:
            row['eating_situation'] = 'C'
        elif row['eating_situation'] == EatingSituationEnum.NOT_EATEN:
            row['eating_situation'] = 'D'
            
        if row['special_diet_features'] == SpecialDietFeaturesEnum.DIABETES_DIET:
            row['special_diet_features'] = 'A'
        elif row['special_diet_features'] == SpecialDietFeaturesEnum.LOW_SALT_DIET:
            row['special_diet_features'] = 'B'
        elif row['special_diet_features'] == SpecialDietFeaturesEnum.HIGH_PROTEIN_DIET:
            row['special_diet_features'] = 'C'
        else:
            row['special_diet_features'] = ''
        return super().customize_row_data(row)

class NewbornTableExportBaseView(ExportByRecordDateBaseView):
    table_file_name = ""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.nid = None
    
    def get_base_queryset(self):
        return super().get_base_queryset().filter(
            newborn__maternity_admission__maternity_center=self.maternity_center,
            newborn__nid=self.nid
        )
    
    def get(self, request):
        self.nid = request.query_params.get('nid')
        if not self.nid:
            return make_response(code=-1, msg="新生儿编号不能为空")
        try:
            newborn = Newborn.objects.select_related(
                'maternity_admission',
                'maternity_admission__maternity',
                'maternity_admission__room'
            ).get(
                nid=self.nid, maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿不存在")
        if newborn.maternity_admission.room:
            self.docx_context['r_num'] = newborn.maternity_admission.room.room_number
        else:
            self.docx_context['r_num'] = "未知"
        
        if newborn.hand_card_number:
            self.docx_context['c_num'] = newborn.hand_card_number
        else:
            self.docx_context['c_num'] = "未知"
        
        if newborn.name:
            self.docx_context['name'] = newborn.name
            self.export_file_name = str(newborn.name) + '-' + self.table_file_name
        else:
            self.docx_context['name'] = "未知"
            self.export_file_name = '新生儿' + self.table_file_name
        
        if newborn.gender:
            self.docx_context['gd'] = dict(GenderEnum.choices)[newborn.gender]
        else:
            self.docx_context['gd'] = "未知"
        
        if newborn.maternity_admission.maternity.name:
            self.docx_context['m_name'] = newborn.maternity_admission.maternity.name
        else:
            self.docx_context['m_name'] = "未知"
        return super().get(request)


class NewbornCareOneRecordExportView(NewbornTableExportBaseView):
    export_model = NewbornCareOneRecord
    export_fields = [
        'record_date', 'days_after_birth', 'weight', 'length', 'cry', 'temperature', 'urine',
        'bowel_movement_frequency', 'bowel_movement_color', 'bowel_movement_consistency',
        'anterior_fontanelle', 'guidance', 'caregiver_signature'
    ]
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_care_one.docx"
    col_width = [5, 5, 8, 5, 3, 5, 3, 5, 3, 3, 3, 9, 3]
    page_height = 34
    fixed_row_count = 17
    table_file_name = "护理记录(1)"
    

class NewbornCareTwoRecordExportView(NewbornTableExportBaseView):
    export_model = NewbornCareTwoRecord
    export_fields = [
        'record_date', 'skin_jaundice', 'skin_ruddy', 'skin_erythema', 'skin_erosion', 
        'skin_pustule', 'skin_eczema', 'skin_diaper_rash',
        'eyes_normal', 'eyes_discharge', 'oral_smooth', 'oral_ulcer', 'oral_thrush',
        'umbilical_dry', 'umbilical_bleeding', 'umbilical_detached',
        'vomiting', 'limb_tone', 'birth_injury', 'jaundice_value', 'caregiver_signature'
    ]
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_care_two.docx"
    col_width = [5, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 2]
    page_height = 33
    fixed_row_count = 11
    table_file_name = "护理记录(2)"
    
    
class NewbornFeedingRecordExportView(NewbornTableExportBaseView):
    export_model = NewbornFeedingRecord
    export_fields = [
        'record_time', 'breast_feeding_left_time', 'breast_feeding_right_time',
        'artificial_feeding_indicators', 'artificial_feeding_amount',
        'mixed_feeding_self_feeding_time', 'mixed_feeding_human_feeding_ml',
        'other_feeding', 'caregiver_signature'
    ]
    export_context_fields = ['feeding_method']
    order_by = 'record_time'
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_feeding.docx"
    col_width = [10, 6, 6, 6, 6, 5, 5, 9, 3]
    page_height = 46
    fixed_row_count = 23
    table_file_name = "喂养记录"
    
    def customize_row_data(self, row):
        row = super().customize_row_data(row)
        if row['feeding_method'] == FeedingMethodChoice.BREAST_FEEDING:
            row['artificial_feeding_indicators'] = ''
            row['artificial_feeding_amount'] = ''
            row['mixed_feeding_self_feeding_time'] = ''
            row['mixed_feeding_human_feeding_ml'] = ''
        elif row['feeding_method'] == FeedingMethodChoice.ARTIFICIAL_FEEDING:
            row['breast_feeding_left_time'] = ''
            row['breast_feeding_right_time'] = ''
            row['mixed_feeding_self_feeding_time'] = ''
            row['mixed_feeding_human_feeding_ml'] = ''
        elif row['feeding_method'] == FeedingMethodChoice.MIXED_FEEDING:
            pass  # 混合喂养时保留所有字段
        
        if 'record_time' in row and row['record_time']:
            if isinstance(row['record_time'], datetime):
                row['record_time'] = parse_datetime_to_shanghai_time(row['record_time'], format_str="%Y-%m-%d\n%H:%M:%S")
        return row
    

class NewbornCareOperationRecordExportView(NewbornTableExportBaseView):
    export_model = NewbornCareOperationRecord
    export_fields = [
        'record_date', 'days_after_birth', 'temperature', 'weight',
        'umbilical_care', 'bath', 'swimming', 'massage', 'acupressure', 'spa',
        'other_situation', 'operator'
    ]
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_care_operation.docx"
    col_width = [5, 5, 5, 4, 4, 4, 4, 4, 4, 14, 9, 3]
    page_height = 54
    fixed_row_count = 18
    table_file_name = "护理操作记录"
    

class ExportSingleRowBaseView(ExportBaseView):
    use_local_time = True
    order_by = None
    is_table = False
    check_mapping = None
    tag_mapping = None
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 自动获取模型的所有字段名（不包括子类字段）
        if self.export_model and not self.export_fields:
            # 获取模型的所有字段
            model_fields = self.export_model._meta.get_fields()
            # 过滤出属于当前模型的字段（不包括继承的字段）
            self.export_fields = []
            for field in model_fields:
                # 只包含当前模型直接定义的字段
                if hasattr(field, 'name') and field.model == self.export_model:
                    self.export_fields.append(field.name)
                    
    def customize_row_data(self, row):
        row = super().customize_row_data(row)
        if not self.check_mapping:
            return row
        context = dict()
        for k, v in self.check_mapping.items():
            try:
                field = self.export_model._meta.get_field(k)
            except Exception:
                continue
            if not field:
                continue
            for idx in range(v[1]):
                context[v[0]+str(idx)] = '☐'
            if row.get(k, None) is None or not str(row[k]).strip():
                continue
            # 单选
            if isinstance(field, models.CharField) and hasattr(field, 'choices'):
                field_choice_values = [choice[0] for choice in field.choices if choice[0] != 'UNKNOWN']
                idx = field_choice_values.index(row[k])
                context[v[0]+str(idx)] = '☑'
            # 多选
            elif isinstance(field, models.JSONField) and hasattr(field, 'validators'):
                field_choice_values = [value for value, label in field.validators[0].choices_display.items() if value != 'UNKNOWN']
                for choice in row[k]:
                    idx = field_choice_values.index(choice)
                    context[v[0]+str(idx)] = '☑'
            # 布尔值
            elif isinstance(field, models.BooleanField):
                if row[k]:
                    context[v[0]+'1'] = '☑'
                else:
                    context[v[0]+'0'] = '☑'
        if not self.tag_mapping:
            return context
        for k, v in self.tag_mapping.items():
            if k in row:
                if row[k] is None or row[k] == '':
                    context[v] = '  '
                else:
                    context[v] = row[k]
        return context


class MaternityCheckInAssessmentExportView(ExportSingleRowBaseView):
    export_model = MaternityCheckInAssessment
    export_template_file_name = "customer_service/core_records/templates/export_templates/maternity_checkin.docx"
    check_mapping = {
        "has_discharge_summary": ('a', 2),
        "is_now_medication": ('b', 2),
        "has_allergic_food": ('c', 2),
        "has_allergic_drug": ('d', 2),
        "is_acute_disease_treatment_period": ('e', 2),
        "is_infectious_disease_period": ('f', 2),
        "has_psychiatric_disease_history": ('g', 2),
        "has_psychological_disease": ('h', 2),
        "emotion": ('i', 5),
        "appetite": ('j', 3),
        "dietary_requirements": ('kk', 2),
        "has_food_preference": ('k', 2),
        "urination": ('l', 2),
        "has_urination_pain": ('m', 2),
        "defecation": ('n', 3),
        "knowledge_of_rehabilitation": ('o', 3),
        "participation_in_pregnancy_education": ('p', 3),
        "family_attitude_to_patient": ('q', 4),
        "face_color": ('r', 3),
        "oral_mucosa": ('s', 2),
        "activity": ('t', 2),
        "incision_position": ('ip', 3),
        "incision_situation": ('u', 2),
        "incision_abnormal": ('v', 4),
        "breast_situation": ('w', 4),
        "nipple_situation": ('x', 2),
        "nipple_abnormal": ('y', 4),
        "milk_situation": ('z', 4)
    }
    tag_mapping = {
        "admission_time": "checkin_time",
        "food_allergic_reaction": "far",
        "drug_allergic_reaction": "dar",
        "discharge_diagnosis": "discharge_diagnosis",
        "pregnancy_complications_and_comorbidities": "pregnancy_cc",
        "delivery_special_situation": "d_special_situation",
        "dro_description": "dro_desc",
        "food_preference_description": "food_pref",
        "allergic_food": "a_food",
        "fatp_pther_description": "fd",
        "temperature": "tp",
        "blood_pressure": "bp",
        "pulse": "ps",
        "respiration": "bh",
        "weight": "wei",
        "height": "hei",
        "activity_limited_part": "alp",
        "lochia_color": "lc",
        "lochia_amount": "la",
        "lochia_smell": "ls",
        "uterus_height": "uh",
        "breast_other_situation": "breast_os",
        "special_findings": "special_findings",
        "previous_pregnancy_history": "prev_ph",
        "maternal_nursing_points": "maternal_np",
        "signature": "a_sign",
        "assessment_time": "a_time"
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.aid = None
    
    def get_base_queryset(self):
        return super().get_base_queryset().filter(
            maternity_admission__maternity_center=self.maternity_center,
            maternity_admission__aid=self.aid
        )
    
    def get(self, request):
        self.aid = request.query_params.get('aid')
        if not self.aid:
            return make_response(code=-1, msg="入院信息编号不能为空")
        try:
            maternity_admission = MaternityAdmission.objects.select_related(
                'maternity', 'room'
            ).get(
                aid=self.aid, maternity_center=request.user.maternity_center
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院信息不存在")
        if maternity_admission.room:
            self.docx_context['r_num'] = maternity_admission.room.room_number
        else:
            self.docx_context['r_num'] = "未知"
        if maternity_admission.maternity.name:
            self.docx_context['name'] = maternity_admission.maternity.name
            self.export_file_name = str(maternity_admission.maternity.name) + '-入住评估单'
        else:
            self.docx_context['name'] = "未知"
            self.export_file_name = '产妇入住评估单'
            
        if maternity_admission.maternity.birth_date:
            today = timezone.now().date()
            birth_date = maternity_admission.maternity.birth_date
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            self.docx_context['age'] = age
        else:
            self.docx_context['age'] = "未知"
        if maternity_admission.maternity.blood_type:
            self.docx_context['bt'] = dict(BloodTypeEnum.choices)[maternity_admission.maternity.blood_type]
        else:
            self.docx_context['bt'] = "未知"
        if maternity_admission.maternity.ethnicity:
            self.docx_context['eth'] = maternity_admission.maternity.ethnicity
        else:
            self.docx_context['eth'] = "未知"
        if maternity_admission.maternity.native_place:
            self.docx_context['np'] = maternity_admission.maternity.native_place
        else:
            self.docx_context['np'] = "未知"

        if maternity_admission.delivery_hospital:
            self.docx_context['d_hospital'] = maternity_admission.delivery_hospital
        else:
            self.docx_context['d_hospital'] = "未知"
        if maternity_admission.actual_delivery_date:
            self.docx_context['d_date'] = maternity_admission.actual_delivery_date.strftime('%Y-%m-%d')
        else:
            self.docx_context['d_date'] = "未知"
        if maternity_admission.delivery_method and maternity_admission.delivery_method in dict(DeliveryMethodEnum.choices):
            self.docx_context['d_method'] = dict(DeliveryMethodEnum.choices)[maternity_admission.delivery_method]
        else:
            self.docx_context['d_method'] = "未知"
        return super().get(request)
    
    def customize_row_data(self, row):
        if row.get('urination'):
            if row['urination'] == UrinationEnum.NORMAL:
                self.docx_context['ur0'] = row['urination_times']
                self.docx_context['ur1'] = '  '
            elif row['urination'] == UrinationEnum.FREQUENT:
                self.docx_context['ur1'] = row['urination_times']
                self.docx_context['ur0'] = '  '
        if row.get('defecation'):
            if row['defecation'] == DefecationEnum.NORMAL:
                self.docx_context['de0'] = row['defecation_times']
                self.docx_context['de1'] = '  '
                self.docx_context['de2'] = '  '
            elif row['defecation'] == DefecationEnum.CONSTIPATION:
                self.docx_context['de1'] = row['defecation_times']
                self.docx_context['de0'] = '  '
                self.docx_context['de2'] = '  '
            elif row['defecation'] == DefecationEnum.DIARRHEA:
                self.docx_context['de2'] = row['defecation_times']
                self.docx_context['de0'] = '  '
                self.docx_context['de1'] = '  '
        return super().customize_row_data(row)
    

class NewbornCheckInAssessmentExportView(ExportSingleRowBaseView):
    export_model = NewbornCheckInAssessment
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_checkin.docx"
    check_mapping = {
        "discharge_summary": ('a', 2),
        "hospitalization_history": ('b', 2),
        "current_medication": ('c', 2),
        "feeding_type": ('d', 3),
        "urination_status": ('e', 2),
        "bowel_movement_status": ('f', 4),
        "complexion": ('g', 2),
        "cry": ('h', 2),
        "reaction": ('i', 2),
        "extremities": ('j', 3),
        "extremity_tone": ('k', 2),
        "birth_injury": ('l', 2),
        "birth_injury_type": ('mm', 2),
        "heart_murmur": ('m', 2),
        "reflexes": ('n', 5),
        "nutrition_development": ('o', 3),
        "skin_status": ('p', 2),
        "skin_abnormality": ('q', 9),
        "anterior_fontanelle": ('r', 3),
        "oral_mucosa": ('s', 2),
        "oral_mucosa_abnormality": ('t', 2),
        "conjunctiva_status": ('u', 2),
        "scleral_jaundice": ('v', 2),
        "congenital_anomaly": ('w', 2),
        "anomaly_type": ('x', 8),
        "umbilical_cord": ('y', 2),
        "umbilical_cord_abnormality": ('z', 3),
        "umbilical_cord_red_and_swollen": ('A', 2),
        "umbilical_cord_fall_off": ('B', 2),
        "sucking_ability": ('C', 3),
        "buttocks": ('D', 2),
        "buttocks_abnormality": ('E', 4),
        "pseudomenstruation": ('F', 2),
        "vaccine_injection": ('G', 3),
        "location": ('H', 4),
        "need_phototherapy": ('I', 2)
    }
    tag_mapping = {
        "admission_time": "checkin_time",
        "blood_type": "bt",
        "apgar_score": "as",
        "hospitalization_disease": "hosp_d",
        "medication_name": "med_name",
        "allergy_history": "ah",
        "special_conditions": "special_conditions",
        "discharge_jaundice": "discharge_j",
        "formula_brand": "f_brand",
        "formula_breastmilk_ratio": "f_ratio",
        "usual_diaper_brand": "d_brand",
        "temperature": "tp",
        "heart_rate": "hr",
        "respiration": "bh",
        "weight": "wei",
        "spo2": "spo2",
        "extremity_restricted_area": "era",
        "birth_injury_skin_lesion_location": "skin",
        "nutrition_delayed_percentile": "ndp",
        "skin_lesion_location": "sl",
        "skin_erythema_location": "se",
        "skin_rash_location": "sr",
        "skin_edema_location": "sd",
        "skin_other_location": "so",
        "head_circumference": "hc",
        "fontanelle_size": "fonta_size",
        "anomaly_type_other": "ato",
        "vaccine_injection_other": "vio",
        "admission_jaundice": "aj",
        "other_special_conditions": "other_special_conditions",
        "baby_care_points": "baby_care_points",
        "signature": "a_sign",
        "assessment_time": "a_time"
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.nid = None
    
    def get_base_queryset(self):
        return super().get_base_queryset().filter(
            newborn__maternity_admission__maternity_center=self.maternity_center,
            newborn__nid=self.nid
        )
    
    # TODO 修改继承结构，使用多继承减少重复代码
    def get(self, request):
        self.nid = request.query_params.get('nid')
        if not self.nid:
            return make_response(code=-1, msg="新生儿编号不能为空")
        try:
            newborn = Newborn.objects.select_related(
                'maternity_admission',
                'maternity_admission__maternity',
                'maternity_admission__room'
            ).get(
                nid=self.nid, maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿不存在")
        if newborn.maternity_admission.room:
            self.docx_context['r_num'] = newborn.maternity_admission.room.room_number
        else:
            self.docx_context['r_num'] = "未知"
        
        if newborn.hand_card_number:
            self.docx_context['c_num'] = newborn.hand_card_number
        else:
            self.docx_context['c_num'] = "未知"
        
        if newborn.name:
            self.docx_context['name'] = newborn.name
            self.export_file_name = str(newborn.name) + '-入住评估单'
        else:
            self.docx_context['name'] = "未知"
            self.export_file_name = '新生儿入住评估单'
        
        if newborn.gender:
            self.docx_context['gd'] = dict(GenderEnum.choices)[newborn.gender]
        else:
            self.docx_context['gd'] = "未知"
        
        if newborn.maternity_admission.maternity.name:
            self.docx_context['m_name'] = newborn.maternity_admission.maternity.name
        else:
            self.docx_context['m_name'] = "未知"
        
        if newborn.birth_time:
            self.docx_context['birth_time'] = timezone.localtime(newborn.birth_time).strftime('%Y-%m-%d %H:%M:%S')
        else:
            self.docx_context['birth_time'] = "未知"
        if newborn.birth_week:
            self.docx_context['birth_week'] = newborn.birth_week
        else:
            self.docx_context['birth_week'] = "未知"
        if newborn.birth_weight:
            self.docx_context['b_wei'] = newborn.birth_weight
        else:
            self.docx_context['b_wei'] = "未知"
        if newborn.birth_length:
            self.docx_context['b_hei'] = newborn.birth_length
        else:
            self.docx_context['b_hei'] = "未知"
        if newborn.maternity_admission.delivery_method and newborn.maternity_admission.delivery_method in dict(DeliveryMethodEnum.choices):
            self.docx_context['d_method'] = dict(DeliveryMethodEnum.choices)[newborn.maternity_admission.delivery_method]
        else:
            self.docx_context['d_method'] = "未知"
        return super().get(request)
    
    def customize_row_data(self, row):
        if row.get('urination_status'):
            if row['urination_status'] == UrinationChoice.NORMAL:
                self.docx_context['ur0'] = row['normal_urination_times']
                self.docx_context['ur1'] = '  '
            elif row['urination_status'] == UrinationChoice.LOW:
                self.docx_context['ur1'] = row['normal_urination_times']
                self.docx_context['ur0'] = '  '
        if row.get('bowel_movement_status'):
            if row['bowel_movement_status'] == BowelMovementChoice.NORMAL:
                self.docx_context['de0'] = row['bowel_times']
                self.docx_context['de1'] = '  '
                self.docx_context['de2'] = '  '
            elif row['bowel_movement_status'] == BowelMovementChoice.CONSTIPATION:
                self.docx_context['de1'] = row['bowel_times']
                self.docx_context['de0'] = '  '
                self.docx_context['de2'] = '  '
            elif row['bowel_movement_status'] == BowelMovementChoice.DIARRHEA:
                self.docx_context['de2'] = row['bowel_times']
                self.docx_context['de0'] = '  '
                self.docx_context['de1'] = '  '
        return super().customize_row_data(row)
    
    
class NewbornMonthAssessmentExportView(ExportSingleRowBaseView):
    export_model = NewbornMonthAssessment
    export_template_file_name = "customer_service/core_records/templates/export_templates/newborn_month.docx"
    check_mapping = {
        "nutrition_development": ('a', 4),
        "complexion": ('b', 2),
        "cry": ('c', 2),
        "reaction": ('d', 3),
        "skin": ('e', 6),
        "feeding_situation": ('f', 3),
        "muscle_tone_and_activity": ('i', 2),
        "current_nursing_problems": ('j', 2),
        "feeding_guidance": ('k', 5),
        "prevent_cold": ('l', 5)
    }
    tag_mapping = {
        "temperature": "tp",
        "weight": "wei",
        "damaged_part": "dp",
        "rash_part": "rp",
        "formula_brand": "f_brand",
        "urine_times": "ur_times",
        "bowel_movement_usual_times": "bt",
        "constipations_times": "ct",
        "diarrhea_times": "dt",
        "muscle_tone_and_activity_limited_part": "mp",
        "current_nursing_problems_description": "cnp",
        "feeding_guidance_other_description": "fgod"
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.nid = None
    
    def get_base_queryset(self):
        return super().get_base_queryset().filter(
            newborn__maternity_admission__maternity_center=self.maternity_center,
            newborn__nid=self.nid
        )
        
    def get(self, request):
        self.nid = request.query_params.get('nid')
        if not self.nid:
            return make_response(code=-1, msg="新生儿编号不能为空")
        try:
            newborn = Newborn.objects.select_related(
                'maternity_admission',
                'maternity_admission__maternity',
                'maternity_admission__room'
            ).prefetch_related(
                'newborn_check_in_assessments'
            ).get(
                nid=self.nid, maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿不存在")
        if newborn.maternity_admission.room:
            self.docx_context['r_num'] = newborn.maternity_admission.room.room_number
        else:
            self.docx_context['r_num'] = "未知"
        if newborn.name:
            self.docx_context['name'] = newborn.name
            self.export_file_name = str(newborn.name) + '-入住评估单'
        else:
            self.docx_context['name'] = "未知"
            self.export_file_name = '新生儿入住评估单'
        if newborn.maternity_admission.maternity.name:
            self.docx_context['m_name'] = newborn.maternity_admission.maternity.name
        else:
            self.docx_context['m_name'] = "未知"
        # 检查新生儿入住评估记录的入所时间
        if newborn.newborn_check_in_assessments.exists():
            first_assessment = newborn.newborn_check_in_assessments.first()
            if first_assessment.admission_time:
                self.docx_context['checkin_time'] = timezone.localtime(first_assessment.admission_time).strftime('%Y-%m-%d %H:%M:%S')
            else:
                self.docx_context['checkin_time'] = "未知"
        else:
            self.docx_context['checkin_time'] = "未知"
        return super().get(request)
    
    def customize_row_data(self, row):
        if row.get('urine_normal'):
            self.docx_context['g'] = '☑'
        else:
            self.docx_context['g'] = '☐'
        if row.get('bowel_movement_normal'):
            self.docx_context['h'] = '☑'
        else:
            self.docx_context['h'] = '☐'
        return super().customize_row_data(row)
