from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDate<PERSON><PERSON><PERSON>ield
from customer_service.checkout.enum import CheckoutStatusEnum
from customer_service.checkout.models import Checkout
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.ward_round.models import MaternityWardRoundRecord, NewBornWardRoundRecord
from organizational_management.charge.serializers import MaternityCostInfoSerializer
from user.serializers import MaternityDesensitizedSerializer
        
        


# 在住产妇选择列表序列化器
class MaternitySelectListSerializer(serializers.ModelSerializer):
    
    maternity_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    check_in_date = serializers.SerializerMethodField()
    expected_checkout_date = serializers.SerializerMethodField()
    cost_info = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid','maternity_name','room_number','check_in_date','expected_checkout_date','cost_info']
    
    def get_maternity_name(self,obj):
        
        name = obj.maternity.name or '-'
        
        return f'{name}'
    
    def get_room_number(self,obj):
        
        room_number = obj.room.room_number or '-'
        
        return f'{room_number}'
    
    def get_check_in_date(self,obj):
        
        check_in_date = obj.actual_check_in_date or None
        
        return f'{check_in_date}'
    
    def get_expected_checkout_date(self,obj):
        
        expected_checkout_date = obj.expected_check_out_date or None
        
        return f'{expected_checkout_date}'

    def get_cost_info(self,obj):
        
        return MaternityCostInfoSerializer(obj.maternity_cost_info.first()).data
    
# 退房列表序列化器
class CheckoutListSerializer(serializers.ModelSerializer):
    aid = serializers.SerializerMethodField()
    maternity_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    check_in_date = serializers.SerializerMethodField()
    expected_checkout_date = serializers.SerializerMethodField()
    actual_checkout_date = serializers.SerializerMethodField()
    checkout_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Checkout
        fields = ['rid','aid','maternity_name','room_number','check_in_date','expected_checkout_date','actual_checkout_date','checkout_status']
        
    def get_aid(self,obj):
        
        return f'{obj.maternity_admission.aid}'
    
    def get_maternity_name(self,obj):
        
        name = obj.maternity_admission.maternity.name or '-'
        
        return f'{name}'
    
    def get_room_number(self,obj):
        
        room_number = obj.maternity_admission.room.room_number or '-'
        
        return f'{room_number}'
    
    def get_check_in_date(self,obj):
        
        check_in_date = obj.maternity_admission.actual_check_in_date or None
        
        return f'{check_in_date}'
    
    def get_expected_checkout_date(self,obj):
        
        expected_checkout_date = obj.maternity_admission.expected_check_out_date or None
        
        return f'{expected_checkout_date}'
    
    def get_actual_checkout_date(self,obj):
        
        actual_checkout_date = obj.actual_checkout_date or None
        
        return f'{actual_checkout_date}'
    
    def get_checkout_status(self,obj):
        
        return f'{CheckoutStatusEnum(obj.checkout_status).label}'
    
    
    

# 退房管理详情序列化器
class CheckoutDetailSerializer(serializers.ModelSerializer):
    
    maternity_info = serializers.SerializerMethodField()
    admission_info = serializers.SerializerMethodField()
    cost_info = serializers.SerializerMethodField()
    checkout_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Checkout
        fields = ['rid','maternity_info','admission_info','cost_info','checkout_status','facility_equipment_check']
        
    def get_maternity_info(self,obj):
        
        return MaternityDesensitizedSerializer(obj.maternity_admission.maternity).data
    
    def get_admission_info(self,obj):
        
        res = {
            'aid':obj.maternity_admission.aid,
            'room_number':obj.maternity_admission.room.room_number,
            'check_in_date':obj.maternity_admission.actual_check_in_date,
            'expected_checkout_date':obj.maternity_admission.expected_check_out_date,
            'actual_checkout_date':obj.actual_checkout_date,
        }
        
        return res
    
    def get_cost_info(self,obj):
        
        return MaternityCostInfoSerializer(obj.maternity_admission.maternity_cost_info.first()).data
    
    def get_checkout_status(self,obj):
        
        return f'{CheckoutStatusEnum(obj.checkout_status).label}'


        
        