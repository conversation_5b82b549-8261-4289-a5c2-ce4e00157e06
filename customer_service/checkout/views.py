from datetime import datetime
from django.db import transaction
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.checkout.enum import CheckoutStatusEnum
from customer_service.checkout.models import Checkout
from organizational_management.charge.models import MaternityCostInfo
from .serializers import CheckoutDetailSerializer, CheckoutListSerializer, MaternitySelectListSerializer
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from permissions.enum import PermissionEnum



# 退房列表
class CheckoutListAPIView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_OUT_VIEW]
    serializer_class = CheckoutListSerializer
    response_msg = "获取退房列表成功"
    error_response_msg = "获取退房列表失败"
    search_fields = ['maternity_admission__maternity__name','maternity_admission__room__room_number']
    
    def get_queryset(self):
        
        base_queryset = Checkout.objects.filter(maternity_center=self.request.user.maternity_center).select_related('maternity_admission__maternity','maternity_admission__room').order_by('-created_at')
        
        
        status = self.request.query_params.get('status','')
        ec = self.request.query_params.get('ec','')
        
        if status:
            base_queryset = base_queryset.filter(checkout_status=status)
            
        if ec:
            base_queryset = base_queryset.filter(maternity_admission__expected_check_out_date=ec)
        
        return base_queryset
    

# 退房管理详情
class CheckoutDetailAPIView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_OUT_VIEW]
    
    def get(self, request,rid):
        
        checkout = Checkout.get_checkout_by_rid(rid=rid,maternity_center=request.user.maternity_center)
        
        if not checkout:
            return make_response(code=1,msg='退房记录不存在')
        
        return make_response(code=0,msg='获取退房管理详情成功',data=CheckoutDetailSerializer(checkout).data)
    



# 退房管理创建
class CheckoutCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_OUT_EDIT]
    
    def calculate_checkout_status(self, ac, ec):
        
        if isinstance(ac, str):
            try:
                ac = datetime.strptime(ac, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('实际退房日期格式错误')
        
        if ac < ec:
            return CheckoutStatusEnum.EARLY_CHECKOUT
        elif ac > ec:
            return CheckoutStatusEnum.DELAY_CHECKOUT
        else:
            return CheckoutStatusEnum.NORMAL_CHECKOUT
    
    def post(self, request,aid):
        
        maternity_admission = MaternityAdmission.get_maternity_admission_by_aid(aid=aid,maternity_center=request.user.maternity_center)
        
        if not maternity_admission:
            return make_response(code=1,msg='产妇入院记录不存在')
        
        if maternity_admission.check_in_status != CheckInStatusEnum.CHECKED_IN:
            return make_response(code=1,msg=f'无法办理退房，当前状态：{CheckInStatusEnum(maternity_admission.check_in_status).label}')
        
        data = request.data.copy()
        
        actual_checkout_date = data.get('actual_checkout_date','')
        facility_equipment_check = data.get('facility_equipment_check','')
        
        if not actual_checkout_date:
            return make_response(code=1,msg='实际退房日期不能为空')
        
        if not facility_equipment_check:
            return make_response(code=1,msg='设施设备检查情况不能为空')
        
        cost_info = MaternityCostInfo.get_maternity_cost_info_by_aid(aid=aid,maternity_center=request.user.maternity_center)
        
        if not cost_info:   
            return make_response(code=1,msg='产妇费用信息不存在')
        
        if cost_info.remaining_amount != 0:
            return make_response(code=1,msg='产妇费用未清缴，无法办理退房')
        
        with transaction.atomic():  
        
            Checkout.objects.create(
                maternity_center=request.user.maternity_center,
                maternity_admission=maternity_admission,
                actual_checkout_date=actual_checkout_date,
                facility_equipment_check=facility_equipment_check,
                checkout_status=self.calculate_checkout_status(actual_checkout_date,maternity_admission.expected_check_out_date)
            )
            
            maternity_admission.check_in_status = CheckInStatusEnum.CHECKED_OUT
            maternity_admission.actual_check_out_date = actual_checkout_date
            maternity_admission.save()
        
        return make_response(code=0,msg='办理退房成功')


# 在住产妇选择列表
class MaternityListAPIView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_OUT_VIEW,PermissionEnum.CHECK_OUT_EDIT]
    
    
    def get(self, request):
        
        queryset = MaternityAdmission.objects.filter(maternity_center=request.user.maternity_center,check_in_status=CheckInStatusEnum.CHECKED_IN).order_by('-actual_check_in_date')
        
        return make_response(code=0,msg='获取在住产妇选择列表成功',data=MaternitySelectListSerializer(queryset,many=True).data)
