from django.urls import path

from .views import MaternityListAPIView, CheckoutCreateView,CheckoutListAPIView,CheckoutDetailAPIView

urlpatterns = [
    # # 退房管理列表
    path('checkout/list/', CheckoutListAPIView.as_view(), name='checkout-list'),
    # # 退房管理详情
    path('checkout/detail/<str:rid>/', CheckoutDetailAPIView.as_view(), name='checkout-detail'),
    # 退房管理创建
    path('checkout/create/<str:aid>/', CheckoutCreateView.as_view(), name='checkout-create'),
    # # 退房管理更新
    # path('checkout/update/<str:rid>/', CheckoutUpdateView.as_view(), name='checkout-update'),
    
    # 在住产妇选择列表
    path('checkout/maternity/list/', MaternityListAPIView.as_view(), name='maternity-list'),
] 